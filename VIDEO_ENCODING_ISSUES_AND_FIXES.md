# Video Encoding Issues and Fixes

## Issues Found

### 1. **Removed Variables Still Referenced**
- The code was still trying to use `selectedStreamType` and `selectedIFrameInterval` variables that were removed
- This was causing JavaScript errors and preventing proper functionality

### 2. **API Response Structure Mismatch**
- Frontend was checking for `currentStreamResult.settings` but backend returns `currentStreamResult.data`
- This prevented current video encoding from being loaded properly

### 3. **Inconsistent Data Access**
- Some places used `.settings` while others used `.data` for API responses
- This caused the video encoding dropdown to not populate with current values

## Fixes Applied

### 1. **Cleaned Up Removed Variable References**
**File**: `src/components/settings/settings/CameraSettings.js`

- Removed all references to `selectedStreamType` and `selectedIFrameInterval`
- Updated auto-loading settings logic
- Updated camera selection reset logic  
- Updated stream settings application logic
- Updated reset button functionality

### 2. **Fixed API Response Structure Access**
**File**: `src/components/settings/settings/CameraSettings.js`

```javascript
// Before (incorrect)
if (currentStreamResult.success && currentStreamResult.settings) {
  setSupportedStreamSettings(selectedCameraIP, supportedStreamResult.settings);
}

// After (correct)
if (currentStreamResult.success && currentStreamResult.data) {
  setSupportedStreamSettings(selectedCameraIP, supportedStreamResult.data);
}
```

### 3. **Added Debug Logging**
Added console.log statements to help debug:
- Supported stream settings received from API
- Current stream settings received from API
- Video encoding dropdown options being rendered
- Video encoding changes being made

## Testing Steps

### 1. **Manual Testing**
1. Open browser developer tools (F12)
2. Go to Settings > Camera Settings
3. Select a camera and test connection
4. Check console for debug messages
5. Try changing video encoding and verify it applies

### 2. **Debug Script Testing**
1. Load the debug script: `test_video_encoding_debug.js`
2. Open browser console
3. Run the debug functions to check data flow

### 3. **API Testing**
Test the API endpoints directly:
```bash
# Get supported settings
GET /api/camera/{camera_ip}/supported-stream-settings?username=admin&password=Admin@123&port=80

# Get current settings  
GET /api/camera/{camera_ip}/current-stream-settings?username=admin&password=Admin@123&port=80

# Set video encoding
POST /api/camera/set-stream-settings
{
  "ip": "*************",
  "username": "admin", 
  "password": "Admin@123",
  "port": 80,
  "videoEncoding": "H.265"
}
```

## Expected Behavior After Fixes

1. **Video Encoding Dropdown Should:**
   - Populate with supported encodings (H.264, H.265)
   - Show current encoding when camera is connected
   - Allow changing between encodings
   - Apply changes via ONVIF when "Apply Settings" is clicked

2. **Console Should Show:**
   - "Supported stream settings received: {data}"
   - "Current stream settings received: {data}"
   - "Video encoding changed to: H.265" (when changed)
   - "Applying video encoding setting: H.265" (when applied)

3. **No JavaScript Errors:**
   - No references to undefined variables
   - No API response structure errors

## Remaining Potential Issues

If video encoding still doesn't work after these fixes, check:

1. **Camera Compatibility**: Not all cameras support changing video encoding via ONVIF
2. **ONVIF Credentials**: Ensure correct username/password/port for ONVIF access
3. **Camera Permissions**: Some cameras require admin privileges to change encoding
4. **Network Issues**: ONVIF communication might be blocked by firewall
5. **Camera Firmware**: Older firmware might not support encoding changes

## Next Steps

1. Test the fixes with an actual camera
2. Remove debug logging once confirmed working
3. Add proper error handling for unsupported cameras
4. Consider adding user feedback for encoding change success/failure
