// Test script to debug video encoding issues
// Run this in the browser console when on the settings page

console.log('=== Video Encoding Debug Test ===');

// Test 1: Check if camera store has cameras
const cameraStore = window.useCameraStore?.getState?.();
console.log('1. Camera Store:', cameraStore);
console.log('   Available cameras:', cameraStore?.cameras?.map(c => c.ip));

// Test 2: Check camera settings store
const settingsStore = window.useCameraSettingsStore?.getState?.();
console.log('2. Settings Store:', settingsStore);

// Test 3: Check supported stream settings for a specific camera
const testCameraIP = '*************'; // Replace with your camera IP
const supportedSettings = settingsStore?.getSupportedStreamSettings?.(testCameraIP);
console.log('3. Supported Settings for', testCameraIP, ':', supportedSettings);
console.log('   Video Encodings:', supportedSettings?.videoEncodings);

// Test 4: Check current stream settings
const currentSettings = settingsStore?.getCurrentStreamSettings?.(testCameraIP);
console.log('4. Current Settings for', testCameraIP, ':', currentSettings);
console.log('   Current Video Encoding:', currentSettings?.videoEncoding);

// Test 5: Test API call directly
async function testVideoEncodingAPI() {
  try {
    console.log('5. Testing API calls...');
    
    // Test supported settings API
    const response1 = await fetch(`/api/camera/${testCameraIP}/supported-stream-settings?username=admin&password=Admin@123&port=80`);
    const supportedData = await response1.json();
    console.log('   Supported Settings API Response:', supportedData);
    
    // Test current settings API
    const response2 = await fetch(`/api/camera/${testCameraIP}/current-stream-settings?username=admin&password=Admin@123&port=80`);
    const currentData = await response2.json();
    console.log('   Current Settings API Response:', currentData);
    
    // Test setting video encoding
    const testPayload = {
      ip: testCameraIP,
      username: 'admin',
      password: 'Admin@123',
      port: 80,
      videoEncoding: 'H.265'
    };
    
    console.log('   Testing video encoding change with payload:', testPayload);
    const response3 = await fetch('/api/camera/set-stream-settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });
    const setResult = await response3.json();
    console.log('   Set Video Encoding API Response:', setResult);
    
  } catch (error) {
    console.error('   API Test Error:', error);
  }
}

// Test 6: Check DOM elements
function checkDOMElements() {
  console.log('6. Checking DOM elements...');
  const videoEncodingSelect = document.querySelector('select[value*="H."]') || 
                             document.querySelector('label:contains("Video Encoding")').nextElementSibling?.querySelector('select');
  console.log('   Video Encoding Select Element:', videoEncodingSelect);
  console.log('   Options:', Array.from(videoEncodingSelect?.options || []).map(opt => ({value: opt.value, text: opt.text})));
}

// Run tests
checkDOMElements();

console.log('\n=== To run API tests, execute: testVideoEncodingAPI() ===');
console.log('=== Make sure to replace testCameraIP with your actual camera IP ===');

// Export test function to global scope
window.testVideoEncodingAPI = testVideoEncodingAPI;
